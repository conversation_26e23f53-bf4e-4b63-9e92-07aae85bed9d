using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;
using System.Drawing;
using System.Linq;

namespace XMLDecryptor
{
    public partial class MainForm : Form
    {
        private TextBox logTextBox;
        private Button selectFolderButton;
        private Button selectFileButton;
        private Button decryptButton;
        private Label statusLabel;
        private string selectedPath = "";

        public MainForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "XML文件解密工具";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.AllowDrop = true;
            this.DragEnter += MainForm_DragEnter;
            this.DragDrop += MainForm_DragDrop;

            // 创建控件
            selectFolderButton = new Button
            {
                Text = "选择文件夹",
                Location = new Point(20, 20),
                Size = new Size(100, 30)
            };
            selectFolderButton.Click += SelectFolderButton_Click;

            selectFileButton = new Button
            {
                Text = "选择文件",
                Location = new Point(140, 20),
                Size = new Size(100, 30)
            };
            selectFileButton.Click += SelectFileButton_Click;

            decryptButton = new Button
            {
                Text = "开始解密",
                Location = new Point(260, 20),
                Size = new Size(100, 30),
                Enabled = false
            };
            decryptButton.Click += DecryptButton_Click;

            statusLabel = new Label
            {
                Text = "请选择要解密的文件或文件夹，或直接拖拽到此窗口",
                Location = new Point(20, 60),
                Size = new Size(550, 20),
                ForeColor = Color.Blue
            };

            logTextBox = new TextBox
            {
                Location = new Point(20, 90),
                Size = new Size(550, 350),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 9)
            };

            // 添加控件到窗体
            this.Controls.Add(selectFolderButton);
            this.Controls.Add(selectFileButton);
            this.Controls.Add(decryptButton);
            this.Controls.Add(statusLabel);
            this.Controls.Add(logTextBox);
        }

        private void MainForm_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                e.Effect = DragDropEffects.Copy;
            }
        }

        private void MainForm_DragDrop(object sender, DragEventArgs e)
        {
            string[] files = (string[])e.Data.GetData(DataFormats.FileDrop);
            if (files.Length > 0)
            {
                selectedPath = files[0];
                UpdateStatus();
            }
        }

        private void SelectFolderButton_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog dialog = new FolderBrowserDialog())
            {
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    selectedPath = dialog.SelectedPath;
                    UpdateStatus();
                }
            }
        }

        private void SelectFileButton_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog dialog = new OpenFileDialog())
            {
                dialog.Filter = "XML文件|*.xml|所有文件|*.*";
                if (dialog.ShowDialog() == DialogResult.OK)
                {
                    selectedPath = dialog.FileName;
                    UpdateStatus();
                }
            }
        }

        private void UpdateStatus()
        {
            if (File.Exists(selectedPath))
            {
                statusLabel.Text = $"已选择文件: {Path.GetFileName(selectedPath)}";
                statusLabel.ForeColor = Color.Green;
                decryptButton.Enabled = true;
            }
            else if (Directory.Exists(selectedPath))
            {
                statusLabel.Text = $"已选择文件夹: {Path.GetFileName(selectedPath)}";
                statusLabel.ForeColor = Color.Green;
                decryptButton.Enabled = true;
            }
            else
            {
                statusLabel.Text = "请选择有效的文件或文件夹";
                statusLabel.ForeColor = Color.Red;
                decryptButton.Enabled = false;
            }
        }

        private void DecryptButton_Click(object sender, EventArgs e)
        {
            logTextBox.Clear();
            LogMessage("开始解密处理...");

            try
            {
                if (File.Exists(selectedPath))
                {
                    // 处理单个文件
                    ProcessFile(selectedPath);
                }
                else if (Directory.Exists(selectedPath))
                {
                    // 处理文件夹中的所有XML文件
                    string[] xmlFiles = Directory.GetFiles(selectedPath, "*.xml", SearchOption.AllDirectories);
                    LogMessage($"找到 {xmlFiles.Length} 个XML文件");

                    foreach (string file in xmlFiles)
                    {
                        ProcessFile(file);
                    }
                }

                LogMessage("解密处理完成！");
            }
            catch (Exception ex)
            {
                LogMessage($"错误: {ex.Message}");
            }
        }

        private void ProcessFile(string filePath)
        {
            try
            {
                LogMessage($"处理文件: {Path.GetFileName(filePath)}");

                string content = File.ReadAllText(filePath, Encoding.UTF8);
                
                // 检查是否需要解密
                if (content.StartsWith("<"))
                {
                    LogMessage($"  文件已是明文格式，跳过");
                    return;
                }

                string decryptedContent = DecryptContent(content);
                
                if (!string.IsNullOrEmpty(decryptedContent))
                {
                    // 生成解密后的文件名
                    string directory = Path.GetDirectoryName(filePath);
                    string fileName = Path.GetFileNameWithoutExtension(filePath);
                    string extension = Path.GetExtension(filePath);
                    string outputPath = Path.Combine(directory, $"decrypt_{fileName}{extension}");

                    File.WriteAllText(outputPath, decryptedContent, Encoding.UTF8);
                    LogMessage($"  解密成功，保存为: decrypt_{fileName}{extension}");
                }
                else
                {
                    LogMessage($"  解密失败");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"  处理文件时出错: {ex.Message}");
            }
        }

        private void LogMessage(string message)
        {
            logTextBox.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\r\n");
            logTextBox.ScrollToCaret();
            Application.DoEvents();
        }

        // 解密内容的主方法
        private string DecryptContent(string content)
        {
            try
            {
                // 检查是否是CRC格式 (数字@内容)
                if (content.Contains("@"))
                {
                    return DecryptCrcFormat(content);
                }
                // 检查是否是ExtractString格式 (以@开头)
                else if (content.StartsWith("@"))
                {
                    return ExtractString(content);
                }
                else
                {
                    // 尝试其他解密方法
                    return DecodeSave(content);
                }
            }
            catch (Exception ex)
            {
                LogMessage($"  解密过程中出错: {ex.Message}");
                return null;
            }
        }

        // CRC格式解密 (对应SaveManager中的crcm方法)
        private string DecryptCrcFormat(string input)
        {
            string[] parts = input.Split(new char[] { '@' });
            if (parts.Length != 2)
            {
                return string.Empty;
            }

            string crcCode = parts[0];
            string encryptedContent = TripleDESDecrypt(parts[1]);
            string calculatedCrc = CalculateCRC16(encryptedContent);

            if (crcCode != calculatedCrc)
            {
                LogMessage($"  CRC校验失败: 期望{crcCode}, 实际{calculatedCrc}");
                return string.Empty;
            }

            return encryptedContent;
        }

        // TripleDES解密 (对应SaveManager中的m方法)
        private string TripleDESDecrypt(string encryptedText)
        {
            UTF8Encoding utf8 = new UTF8Encoding();
            MD5CryptoServiceProvider md5 = new MD5CryptoServiceProvider();
            byte[] key = md5.ComputeHash(utf8.GetBytes("Yh$45Ct@mods"));

            TripleDESCryptoServiceProvider tripleDES = new TripleDESCryptoServiceProvider
            {
                Key = key,
                Mode = CipherMode.ECB,
                Padding = PaddingMode.PKCS7
            };

            byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
            byte[] decryptedBytes;

            try
            {
                decryptedBytes = tripleDES.CreateDecryptor().TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
            }
            finally
            {
                tripleDES.Clear();
                md5.Clear();
            }

            return utf8.GetString(decryptedBytes);
        }

        // CRC16计算 (对应SaveManager中的CRC16_C方法)
        private string CalculateCRC16(string str)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            byte crcHigh = 255;
            byte crcLow = 255;
            byte poly1 = 1;
            byte poly2 = 160;

            foreach (byte b in bytes)
            {
                crcHigh ^= b;
                for (int i = 0; i <= 7; i++)
                {
                    byte tempHigh = crcLow;
                    byte tempLow = crcHigh;
                    crcLow = (byte)(crcLow >> 1);
                    crcHigh = (byte)(crcHigh >> 1);

                    if ((tempHigh & 1) == 1)
                    {
                        crcHigh |= 128;
                    }

                    if ((tempLow & 1) == 1)
                    {
                        crcLow ^= poly2;
                        crcHigh ^= poly1;
                    }
                }
            }

            return string.Format("{0}{1}", crcLow, crcHigh);
        }

        // ExtractString解密 (对应SaveManager中的ExtractString方法)
        private string ExtractString(string str)
        {
            str = Base64Decode(str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2)
                .Replace("/", "")
                .Replace("#", "/"));

            StringBuilder result = new StringBuilder();
            for (int i = 0; i < str.Length; i += 2)
            {
                int count = (int)(str[i + 1] - '0');
                for (int j = 0; j < count; j++)
                {
                    result.Append(str[i]);
                }
            }
            return result.ToString();
        }

        // Base64解码
        private string Base64Decode(string encodedText)
        {
            try
            {
                byte[] bytes = Convert.FromBase64String(encodedText);
                return Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return string.Empty;
            }
        }

        // 保存文件解密 (对应SaveManager中的Decode_Save方法)
        private string DecodeSave(string result)
        {
            if (result.Substring(result.Length - 1, 1) == "@")
            {
                result = result.Substring(0, result.Length - 1) + "=";
            }
            result = result.Substring(1, result.Length - 1);
            result = result.Replace("#", "").Replace("$", "/");
            result = GetResult(result);
            return Base64Decode(result);
        }

        // 字符转换 (对应SaveManager中的getResult方法)
        private string GetResult(string input)
        {
            char[] upperChars = new char[]
            {
                'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P',
                'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z',
                'X', 'C', 'V', 'B', 'N', 'M'
            };
            char[] lowerChars = new char[]
            {
                'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p',
                'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
                'x', 'c', 'v', 'b', 'n', 'm'
            };

            StringBuilder result = new StringBuilder();
            for (int i = 0; i < input.Length; i++)
            {
                int upperIndex = Array.IndexOf(upperChars, input[i]);
                if (upperIndex == -1)
                {
                    int lowerIndex = Array.IndexOf(lowerChars, input[i]);
                    if (lowerIndex == -1)
                    {
                        result.Append(input[i]);
                    }
                    else
                    {
                        result.Append(upperChars[lowerIndex]);
                    }
                }
                else
                {
                    result.Append(lowerChars[upperIndex]);
                }
            }
            return result.ToString();
        }
    }

    // 程序入口点
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new MainForm());
        }
    }
}
