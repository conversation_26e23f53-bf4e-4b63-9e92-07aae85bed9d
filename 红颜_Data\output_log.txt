Initialize engine version: 5.2.4f1 (98095704e6fe)
GfxDevice: creating device client; threaded=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     128 MB
Begin MonoManager ReloadAssembly
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp-firstpass.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp-firstpass.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.UI.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.UI.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.Networking.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.Networking.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween43.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween43.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween46.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween46.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Debuger.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Debuger.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.Core.dll (this message is harmless)
- Completed reload, in  0.044 seconds
<RI> Initializing input.

<RI> Input initialized.

desktop: 1920x1080 60Hz; virtual: 1920x1080 at 0,0
<RI> Initialized touch support.

Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.Xml.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.Configuration.dll (this message is harmless)
Lua function ROOT_getLuaFiles not exists
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

调用了未定义的lua 函数:ROOT_getLuaFiles
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.NullReferenceException: Object reference not set to an instance of an object

  at JyGame.LuaManager.Init (Boolean forceReset) [0x00000] in <filename unknown>:0 
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

Unloading 4 Unused Serialized files (Serialized files now loaded: 0)
Setting up 4 worker threads for Enlighten.
  Thread -> id: 5740 -> priority: 1 
  Thread -> id: 44b0 -> priority: 1 
  Thread -> id: 6690 -> priority: 1 
  Thread -> id: 5ba8 -> priority: 1 
UnloadTime: 2.883500 ms

Unloading 25 unused Assets to reduce memory usage. Loaded Objects now: 506.
Total: 0.509400 ms (FindLiveObjects: 0.035400 ms CreateObjectMapping: 0.010500 ms MarkObjects: 0.450500 ms  DeleteObjects: 0.012500 ms)

Lua function ROOT_getConfigList not exists
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

调用了未定义的lua 函数:ROOT_getConfigList
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

Unloading 1 Unused Serialized files (Serialized files now loaded: 0)

Unloading 32 unused Assets to reduce memory usage. Loaded Objects now: 533.
Total: 0.708700 ms (FindLiveObjects: 0.036800 ms CreateObjectMapping: 0.016500 ms MarkObjects: 0.632200 ms  DeleteObjects: 0.022500 ms)




System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

The referenced script on this Behaviour is missing!
 
(Filename:  Line: 1649)

Unloading 2 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.797200 ms
Lua function ROOT_getLuaFiles not exists
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

调用了未定义的lua 函数:ROOT_getLuaFiles
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.NullReferenceException: Object reference not set to an instance of an object

  at JyGame.LuaManager.Init (Boolean forceReset) [0x00000] in <filename unknown>:0 
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

Lua function ROOT_getConfigList not exists
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

调用了未定义的lua 函数:ROOT_getConfigList
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

NullReferenceException: Object reference not set to an instance of an object
  at JyGame.LuaManager.GetConfig[String[]] (System.String key) [0x00000] in <filename unknown>:0 

  at JyGame.CommonSettings.get_MAINMENU_BG () [0x00000] in <filename unknown>:0 

  at JyGame.CommonSettings.GetRandom_MAINMENU_BG () [0x00000] in <filename unknown>:0 

  at MainMenu.ShowMainMenu () [0x00000] in <filename unknown>:0 

  at MainMenu.Start () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)


Unloading 30 unused Assets to reduce memory usage. Loaded Objects now: 870.
Total: 40.005699 ms (FindLiveObjects: 0.024400 ms CreateObjectMapping: 0.016100 ms MarkObjects: 39.938900 ms  DeleteObjects: 0.025700 ms)

读取存档失败: XML解析错误!
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

Unloading 3 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 1.515100 ms

Unloading 73 unused Assets to reduce memory usage. Loaded Objects now: 547.
Total: 38.925900 ms (FindLiveObjects: 0.044300 ms CreateObjectMapping: 0.014800 ms MarkObjects: 38.821499 ms  DeleteObjects: 0.044600 ms)

Unloading 0 Unused Serialized files (Serialized files now loaded: 5)

Unloading 12 unused Assets to reduce memory usage. Loaded Objects now: 549.
Total: 41.755299 ms (FindLiveObjects: 0.020700 ms CreateObjectMapping: 0.015100 ms MarkObjects: 41.694801 ms  DeleteObjects: 0.023900 ms)

Unloading 4 Unused Serialized files (Serialized files now loaded: 5)
UnloadTime: 0.438000 ms
Lua function ROLLROLE_start not exists
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

调用了未定义的lua 函数:ROLLROLE_start
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)


Unloading 5 unused Assets to reduce memory usage. Loaded Objects now: 1828.
Total: 39.487000 ms (FindLiveObjects: 0.050900 ms CreateObjectMapping: 0.027500 ms MarkObjects: 39.388199 ms  DeleteObjects: 0.019900 ms)

