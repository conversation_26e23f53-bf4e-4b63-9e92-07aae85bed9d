Initialize engine version: 5.2.4f1 (98095704e6fe)
GfxDevice: creating device client; threaded=1
Direct3D:
    Version:  Direct3D 11.0 [level 11.0]
    Renderer: Intel(R) Iris(R) Xe Graphics (ID=0x46a6)
    Vendor:   Intel
    VRAM:     128 MB
Begin MonoManager ReloadAssembly
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp-firstpass.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp-firstpass.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Assembly-CSharp.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.UI.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.UI.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.Networking.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\UnityEngine.Networking.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween43.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween43.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween46.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\DOTween46.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Debuger.dll (this message is harmless)
Loading D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\Debuger.dll into Unity Child Domain
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.Core.dll (this message is harmless)
- Completed reload, in  0.038 seconds
<RI> Initializing input.

<RI> Input initialized.

desktop: 1920x1080 60Hz; virtual: 1920x1080 at 0,0
<RI> Initialized touch support.

Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.Xml.dll (this message is harmless)
Platform assembly: D:\QuarkNetdiskDownload\[PC]红颜赞助250625\红颜_Data\Managed\System.Configuration.dll (this message is harmless)
LuaInterface.LuaScriptException: [string "jygame/test.lua"]:1: unexpected symbol near '208185'
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

LuaInterface.LuaScriptException: [string "jygame/rollrole.lua"]:1: unexpected symbol near '13795'
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

Unloading 4 Unused Serialized files (Serialized files now loaded: 0)
Setting up 4 worker threads for Enlighten.
  Thread -> id: 58d8 -> priority: 1 
  Thread -> id: 72a4 -> priority: 1 
  Thread -> id: 66f4 -> priority: 1 
  Thread -> id: 50f8 -> priority: 1 
UnloadTime: 2.091400 ms

Unloading 25 unused Assets to reduce memory usage. Loaded Objects now: 506.
Total: 0.422100 ms (FindLiveObjects: 0.016900 ms CreateObjectMapping: 0.009400 ms MarkObjects: 0.384800 ms  DeleteObjects: 0.010500 ms)

Unloading 1 Unused Serialized files (Serialized files now loaded: 0)

Unloading 32 unused Assets to reduce memory usage. Loaded Objects now: 533.
Total: 0.652000 ms (FindLiveObjects: 0.024900 ms CreateObjectMapping: 0.015600 ms MarkObjects: 0.586400 ms  DeleteObjects: 0.024700 ms)




System.TypeInitializationException: An exception was thrown by the type initializer for Mono.CSharp.CSharpCodeCompiler ---> System.IO.FileNotFoundException: 

  at Mono.CSharp.CSharpCodeCompiler..cctor () [0x00000] in <filename unknown>:0 

  --- End of inner exception stack trace ---

  at Microsoft.CSharp.CSharpCodeProvider.CreateCompiler () [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.GenerateSerializers (System.Xml.Serialization.GenerationBatch batch, System.CodeDom.Compiler.CompilerParameters cp) [0x00000] in <filename unknown>:0 

  at System.Xml.Serialization.XmlSerializer.RunSerializerGeneration (System.Object obj) [0x00000] in <filename unknown>:0 

xml载入错误:resource_suggesttips.xml
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.Xml.XmlException: Text node cannot appear in this state.  Line 1, position 1.

  at Mono.Xml2.XmlTextReader.ReadText (Boolean notWhitespace) [0x00000] in <filename unknown>:0 

  at Mono.Xml2.XmlTextReader.ReadContent () [0x00000] in <filename unknown>:0 

  at Mono.Xml2.XmlTextReader.Read () [0x00000] in <filename unknown>:0 

  at System.Xml.XmlTextReader.Read () [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.ReadNodeCore (System.Xml.XmlReader reader) [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.ReadNode (System.Xml.XmlReader reader) [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.Load (System.Xml.XmlReader xmlReader) [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.LoadXml (System.String xml) [0x00000] in <filename unknown>:0 

  at JyGame.ResourceManager.LoadXmlWithConvert (System.String path, System.Xml.XmlDocument xmlDocument, System.String xml) [0x00000] in <filename unknown>:0 

  at JyGame.ResourceManager.LoadResource[Resource] (System.String uri, System.String nodepath) [0x00000] in <filename unknown>:0 
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

xml载入错误:resource.xml
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

System.Xml.XmlException: Text node cannot appear in this state.  Line 1, position 1.

  at Mono.Xml2.XmlTextReader.ReadText (Boolean notWhitespace) [0x00000] in <filename unknown>:0 

  at Mono.Xml2.XmlTextReader.ReadContent () [0x00000] in <filename unknown>:0 

  at Mono.Xml2.XmlTextReader.Read () [0x00000] in <filename unknown>:0 

  at System.Xml.XmlTextReader.Read () [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.ReadNodeCore (System.Xml.XmlReader reader) [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.ReadNode (System.Xml.XmlReader reader) [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.Load (System.Xml.XmlReader xmlReader) [0x00000] in <filename unknown>:0 

  at System.Xml.XmlDocument.LoadXml (System.String xml) [0x00000] in <filename unknown>:0 

  at JyGame.ResourceManager.LoadXmlWithConvert (System.String path, System.Xml.XmlDocument xmlDocument, System.String xml) [0x00000] in <filename unknown>:0 

  at JyGame.ResourceManager.LoadResource[Resource] (System.String uri, System.String nodepath) [0x00000] in <filename unknown>:0 
 
(Filename: C:/buildslave/unity/build/artifacts/generated/common/runtime/UnityEngineDebugBindings.gen.cpp Line: 64)

NullReferenceException: Object reference not set to an instance of an object
  at LoadingUI.<Start>b__0_1 () [0x00000] in <filename unknown>:0 

  at JyGame.AssetBundleManager+<>c__DisplayClass22_0.<LoadAssetBundles>b__4 () [0x00000] in <filename unknown>:0 

  at JyGame.AssetBundleManager+<InitAssetBundle>d__0.MoveNext () [0x00000] in <filename unknown>:0 
 
(Filename:  Line: -1)

