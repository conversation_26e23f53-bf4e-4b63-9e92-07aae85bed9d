using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace TestDecryption
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("测试XML解密功能...");
            
            // 读取加密的menpai.xml文件
            string basePath = @"d:\QuarkNetdiskDownload\[PC]红颜赞助250625";
            string encryptedFile = Path.Combine(basePath, @"gamedata\suyu\HYYM\Scripts\menpai.xml");
            string expectedFile = Path.Combine(basePath, @"gamedata\suyu\HYYM\Scripts\decrypt_menpai.xml");
            
            if (!File.Exists(encryptedFile))
            {
                Console.WriteLine($"找不到加密文件: {encryptedFile}");
                return;
            }
            
            if (!File.Exists(expectedFile))
            {
                Console.WriteLine($"找不到期望的解密文件: {expectedFile}");
                return;
            }
            
            try
            {
                string encryptedContent = File.ReadAllText(encryptedFile, Encoding.UTF8);
                string expectedContent = File.ReadAllText(expectedFile, Encoding.UTF8);
                
                Console.WriteLine($"加密内容长度: {encryptedContent.Length}");
                Console.WriteLine($"期望内容长度: {expectedContent.Length}");
                Console.WriteLine($"加密内容前50字符: {encryptedContent.Substring(0, Math.Min(50, encryptedContent.Length))}");
                
                string decryptedContent = DecryptContent(encryptedContent);
                
                if (!string.IsNullOrEmpty(decryptedContent))
                {
                    Console.WriteLine($"解密成功！解密内容长度: {decryptedContent.Length}");
                    Console.WriteLine($"解密内容前100字符: {decryptedContent.Substring(0, Math.Min(100, decryptedContent.Length))}");
                    
                    // 保存解密结果用于比较
                    string outputPath = Path.Combine(basePath, "test_decrypt_menpai.xml");
                    File.WriteAllText(outputPath, decryptedContent, Encoding.UTF8);
                    Console.WriteLine($"解密结果已保存为: {outputPath}");
                    
                    // 比较结果
                    if (decryptedContent.Trim() == expectedContent.Trim())
                    {
                        Console.WriteLine("✓ 解密结果与期望文件完全匹配！");
                    }
                    else
                    {
                        Console.WriteLine("✗ 解密结果与期望文件不匹配");
                        Console.WriteLine($"期望内容前100字符: {expectedContent.Substring(0, Math.Min(100, expectedContent.Length))}");
                    }
                }
                else
                {
                    Console.WriteLine("解密失败！");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出错: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }
            
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        
        static string DecryptContent(string content)
        {
            try
            {
                // 检查是否是CRC格式 (数字@内容)
                if (content.Contains("@"))
                {
                    return DecryptCrcFormat(content);
                }
                // 检查是否是ExtractString格式 (以@开头)
                else if (content.StartsWith("@"))
                {
                    return ExtractString(content);
                }
                else
                {
                    // 尝试其他解密方法
                    return DecodeSave(content);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"解密过程中出错: {ex.Message}");
                return null;
            }
        }

        // CRC格式解密 (对应SaveManager中的crcm方法)
        static string DecryptCrcFormat(string input)
        {
            string[] parts = input.Split(new char[] { '@' });
            if (parts.Length != 2)
            {
                Console.WriteLine("CRC格式错误：分割后不是2部分");
                return string.Empty;
            }

            string crcCode = parts[0];
            Console.WriteLine($"CRC校验码: {crcCode}");
            
            string encryptedContent = TripleDESDecrypt(parts[1]);
            if (string.IsNullOrEmpty(encryptedContent))
            {
                Console.WriteLine("TripleDES解密失败");
                return string.Empty;
            }
            
            string calculatedCrc = CalculateCRC16(encryptedContent);
            Console.WriteLine($"计算的CRC: {calculatedCrc}");

            if (crcCode != calculatedCrc)
            {
                Console.WriteLine($"CRC校验失败: 期望{crcCode}, 实际{calculatedCrc}");
                return string.Empty;
            }

            Console.WriteLine("CRC校验通过");
            return encryptedContent;
        }

        // TripleDES解密 (对应SaveManager中的m方法)
        static string TripleDESDecrypt(string encryptedText)
        {
            try
            {
                UTF8Encoding utf8 = new UTF8Encoding();
                using (MD5 md5 = MD5.Create())
                {
                    byte[] key = md5.ComputeHash(utf8.GetBytes("Yh$45Ct@mods"));
                    
                    using (TripleDES tripleDES = TripleDES.Create())
                    {
                        tripleDES.Key = key;
                        tripleDES.Mode = CipherMode.ECB;
                        tripleDES.Padding = PaddingMode.PKCS7;

                        byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                        byte[] decryptedBytes = tripleDES.CreateDecryptor().TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
                        
                        return utf8.GetString(decryptedBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TripleDES解密错误: {ex.Message}");
                return string.Empty;
            }
        }

        // CRC16计算 (对应SaveManager中的CRC16_C方法)
        static string CalculateCRC16(string str)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            byte crcHigh = 255;
            byte crcLow = 255;
            byte poly1 = 1;
            byte poly2 = 160;

            foreach (byte b in bytes)
            {
                crcHigh ^= b;
                for (int i = 0; i <= 7; i++)
                {
                    byte tempHigh = crcLow;
                    byte tempLow = crcHigh;
                    crcLow = (byte)(crcLow >> 1);
                    crcHigh = (byte)(crcHigh >> 1);
                    
                    if ((tempHigh & 1) == 1)
                    {
                        crcHigh |= 128;
                    }
                    
                    if ((tempLow & 1) == 1)
                    {
                        crcLow ^= poly2;
                        crcHigh ^= poly1;
                    }
                }
            }

            return string.Format("{0}{1}", crcLow, crcHigh);
        }

        // ExtractString解密 (对应SaveManager中的ExtractString方法)
        static string ExtractString(string str)
        {
            str = Base64Decode(str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2)
                .Replace("/", "")
                .Replace("#", "/"));
            
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < str.Length; i += 2)
            {
                int count = (int)(str[i + 1] - '0');
                for (int j = 0; j < count; j++)
                {
                    result.Append(str[i]);
                }
            }
            return result.ToString();
        }

        // Base64解码
        static string Base64Decode(string encodedText)
        {
            try
            {
                byte[] bytes = Convert.FromBase64String(encodedText);
                return Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return string.Empty;
            }
        }

        // 保存文件解密 (对应SaveManager中的Decode_Save方法)
        static string DecodeSave(string result)
        {
            if (result.Substring(result.Length - 1, 1) == "@")
            {
                result = result.Substring(0, result.Length - 1) + "=";
            }
            result = result.Substring(1, result.Length - 1);
            result = result.Replace("#", "").Replace("$", "/");
            result = GetResult(result);
            return Base64Decode(result);
        }

        // 字符转换 (对应SaveManager中的getResult方法)
        static string GetResult(string input)
        {
            char[] upperChars = new char[]
            {
                'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P',
                'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z',
                'X', 'C', 'V', 'B', 'N', 'M'
            };
            char[] lowerChars = new char[]
            {
                'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p',
                'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
                'x', 'c', 'v', 'b', 'n', 'm'
            };

            StringBuilder result = new StringBuilder();
            for (int i = 0; i < input.Length; i++)
            {
                int upperIndex = Array.IndexOf(upperChars, input[i]);
                if (upperIndex == -1)
                {
                    int lowerIndex = Array.IndexOf(lowerChars, input[i]);
                    if (lowerIndex == -1)
                    {
                        result.Append(input[i]);
                    }
                    else
                    {
                        result.Append(upperChars[lowerIndex]);
                    }
                }
                else
                {
                    result.Append(lowerChars[upperIndex]);
                }
            }
            return result.ToString();
        }
    }
}
