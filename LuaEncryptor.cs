using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace LuaEncryptor
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("Lua文件重新加密工具");
            Console.WriteLine("====================");
            Console.WriteLine("将明文lua文件重新加密为CRC格式");
            Console.WriteLine();
            
            if (args.Length == 0)
            {
                Console.WriteLine("用法: LuaEncryptor <lua文件路径>");
                Console.WriteLine("示例: LuaEncryptor main.lua");
                return;
            }
            
            string inputFile = args[0];
            
            if (!File.Exists(inputFile))
            {
                Console.WriteLine($"文件不存在: {inputFile}");
                return;
            }
            
            try
            {
                string content = File.ReadAllText(inputFile, Encoding.UTF8);
                string encryptedContent = EncryptToCrcFormat(content);
                
                string outputFile = Path.GetFileNameWithoutExtension(inputFile) + "_encrypted" + Path.GetExtension(inputFile);
                File.WriteAllText(outputFile, encryptedContent, Encoding.UTF8);
                
                Console.WriteLine($"加密成功！");
                Console.WriteLine($"输入文件: {inputFile}");
                Console.WriteLine($"输出文件: {outputFile}");
                Console.WriteLine($"原文件长度: {content.Length}");
                Console.WriteLine($"加密后长度: {encryptedContent.Length}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加密失败: {ex.Message}");
            }
            
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        
        // 加密为CRC格式 (对应SaveManager中的crcjm方法)
        static string EncryptToCrcFormat(string input)
        {
            string encryptedContent = TripleDESEncrypt(input);
            string crcCode = CalculateCRC16(input);
            return crcCode + "@" + encryptedContent;
        }
        
        // TripleDES加密 (对应SaveManager中的jm方法)
        static string TripleDESEncrypt(string plainText)
        {
            try
            {
                UTF8Encoding utf8 = new UTF8Encoding();
                using (MD5 md5 = MD5.Create())
                {
                    byte[] key = md5.ComputeHash(utf8.GetBytes("Yh$45Ct@mods"));
                    
                    using (TripleDES tripleDES = TripleDES.Create())
                    {
                        tripleDES.Key = key;
                        tripleDES.Mode = CipherMode.ECB;
                        tripleDES.Padding = PaddingMode.PKCS7;

                        byte[] plainBytes = utf8.GetBytes(plainText);
                        byte[] encryptedBytes = tripleDES.CreateEncryptor().TransformFinalBlock(plainBytes, 0, plainBytes.Length);
                        
                        return Convert.ToBase64String(encryptedBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TripleDES加密错误: {ex.Message}");
                return string.Empty;
            }
        }
        
        // CRC16计算 (对应SaveManager中的CRC16_C方法)
        static string CalculateCRC16(string str)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            byte crcHigh = 255;
            byte crcLow = 255;
            byte poly1 = 1;
            byte poly2 = 160;

            foreach (byte b in bytes)
            {
                crcHigh ^= b;
                for (int i = 0; i <= 7; i++)
                {
                    byte tempHigh = crcLow;
                    byte tempLow = crcHigh;
                    crcLow = (byte)(crcLow >> 1);
                    crcHigh = (byte)(crcHigh >> 1);
                    
                    if ((tempHigh & 1) == 1)
                    {
                        crcHigh |= 128;
                    }
                    
                    if ((tempLow & 1) == 1)
                    {
                        crcLow ^= poly2;
                        crcHigh ^= poly1;
                    }
                }
            }

            return string.Format("{0}{1}", crcLow, crcHigh);
        }
    }
}
