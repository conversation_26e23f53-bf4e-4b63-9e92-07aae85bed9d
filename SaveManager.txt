using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace JyGame
{
	// Token: 0x02000258 RID: 600
	public class SaveManager
	{
		// Token: 0x170001D9 RID: 473
		// (get) Token: 0x06001658 RID: 5720 RVA: 0x000B331C File Offset: 0x000B151C
		public static string SaveDir
		{
			get
			{
				string text = string.Empty;
				text = ((GlobalData.CurrentMod != null) ? (CommonSettings.persistentDataPath + "/suyu/" + GlobalData.CurrentMod.key + "/saves/") : (CommonSettings.persistentDataPath + "/saves/"));
				if (!Directory.Exists(text))
				{
					Directory.CreateDirectory(text);
				}
				return text;
			}
		}

		// Token: 0x06001659 RID: 5721 RVA: 0x000B337C File Offset: 0x000B157C
		public static string GetSave(string saveName)
		{
			RuntimeData.Instance.LastLoadingTime = new TimeSpan(DateTime.Now.Ticks);
			string text = SaveManager.SaveDir + saveName;
			bool flag = !File.Exists(text);
			string text2;
			if (flag)
			{
				text2 = string.Empty;
			}
			else
			{
				ModData.Load();
				using (StreamReader streamReader = new StreamReader(text))
				{
					string text3 = streamReader.ReadToEnd();
					bool flag2 = CommonSettings.EN_SAVE() != 0;
					if (flag2)
					{
						text2 = SaveManager.GetDecode(text3, false);
					}
					else
					{
						text2 = text3;
					}
				}
			}
			return text2;
		}

		// Token: 0x0600165A RID: 5722 RVA: 0x000B3420 File Offset: 0x000B1620
		public static void SetSave(string saveName, string content)
		{
			using (StreamWriter streamWriter = new StreamWriter(SaveManager.SaveDir + saveName))
			{
				bool flag = CommonSettings.EN_SAVE() != 0;
				if (flag)
				{
					content = SaveManager.SetEncrypt(content);
				}
				streamWriter.Write(content);
			}
			bool flag2 = !(saveName == "autosave") || RuntimeData.Instance.AutoSaveOnly;
			if (flag2)
			{
				ModData.Save();
			}
		}

		// Token: 0x0600165B RID: 5723 RVA: 0x000B34A4 File Offset: 0x000B16A4
		public static void DeleteSave(string saveName)
		{
			string text = SaveManager.SaveDir + saveName;
			bool flag = File.Exists(text);
			if (flag)
			{
				File.Delete(text);
			}
		}

		// Token: 0x0600165C RID: 5724 RVA: 0x000B34D4 File Offset: 0x000B16D4
		public static bool ExistSave(string saveName)
		{
			string text = SaveManager.SaveDir + saveName;
			return File.Exists(text);
		}

		// Token: 0x0600165D RID: 5725 RVA: 0x000B34F8 File Offset: 0x000B16F8
		public static string crcjm(string input)
		{
			string text = SaveManager.jm(input);
			string text2 = SaveManager.CRC16_C(input);
			return text2 + "@" + text;
		}

		// Token: 0x0600165E RID: 5726 RVA: 0x000B3524 File Offset: 0x000B1724
		public static string crcm(string input)
		{
			string[] array = input.Split(new char[] { '@' });
			bool flag = array.Length != 2;
			string text;
			if (flag)
			{
				text = string.Empty;
			}
			else
			{
				string text2 = array[0];
				string text3 = SaveManager.m(array[1]);
				string text4 = SaveManager.CRC16_C(text3);
				bool flag2 = text2 != text4;
				if (flag2)
				{
					text = string.Empty;
				}
				else
				{
					text = text3;
				}
			}
			return text;
		}

		// Token: 0x0600165F RID: 5727 RVA: 0x000B3590 File Offset: 0x000B1790
		private static string jm(string Message)
		{
			UTF8Encoding utf8Encoding = new UTF8Encoding();
			MD5CryptoServiceProvider md5CryptoServiceProvider = new MD5CryptoServiceProvider();
			byte[] array = md5CryptoServiceProvider.ComputeHash(utf8Encoding.GetBytes("Yh$45Ct@mods"));
			TripleDESCryptoServiceProvider tripleDESCryptoServiceProvider = new TripleDESCryptoServiceProvider
			{
				Key = array,
				Mode = CipherMode.ECB,
				Padding = PaddingMode.PKCS7
			};
			byte[] bytes = utf8Encoding.GetBytes(Message);
			byte[] array2;
			try
			{
				array2 = tripleDESCryptoServiceProvider.CreateEncryptor().TransformFinalBlock(bytes, 0, bytes.Length);
			}
			finally
			{
				tripleDESCryptoServiceProvider.Clear();
				md5CryptoServiceProvider.Clear();
			}
			return Convert.ToBase64String(array2);
		}

		// Token: 0x06001660 RID: 5728 RVA: 0x000B3630 File Offset: 0x000B1830
		private static string m(string Message)
		{
			UTF8Encoding utf8Encoding = new UTF8Encoding();
			MD5CryptoServiceProvider md5CryptoServiceProvider = new MD5CryptoServiceProvider();
			byte[] array = md5CryptoServiceProvider.ComputeHash(utf8Encoding.GetBytes("Yh$45Ct@mods"));
			TripleDESCryptoServiceProvider tripleDESCryptoServiceProvider = new TripleDESCryptoServiceProvider
			{
				Key = array,
				Mode = CipherMode.ECB,
				Padding = PaddingMode.PKCS7
			};
			byte[] array2 = Convert.FromBase64String(Message);
			byte[] array3;
			try
			{
				array3 = tripleDESCryptoServiceProvider.CreateDecryptor().TransformFinalBlock(array2, 0, array2.Length);
			}
			finally
			{
				tripleDESCryptoServiceProvider.Clear();
				md5CryptoServiceProvider.Clear();
			}
			return utf8Encoding.GetString(array3);
		}

		// Token: 0x06001661 RID: 5729 RVA: 0x000B36D0 File Offset: 0x000B18D0
		private static string CRC16_C(string str)
		{
			byte[] bytes = Encoding.UTF8.GetBytes(str);
			byte b = byte.MaxValue;
			byte b2 = byte.MaxValue;
			byte b3 = 1;
			byte b4 = 160;
			byte[] array = bytes;
			for (int i = 0; i < array.Length; i++)
			{
				b ^= array[i];
				for (int j = 0; j <= 7; j++)
				{
					byte b5 = b2;
					byte b6 = b;
					b2 = (byte)(b2 >> 1);
					b = (byte)(b >> 1);
					bool flag = (b5 & 1) == 1;
					if (flag)
					{
						b |= 128;
					}
					bool flag2 = (b6 & 1) == 1;
					if (flag2)
					{
						b2 ^= b4;
						b ^= b3;
					}
				}
			}
			return string.Format("{0}{1}", b2, b);
		}

		// Token: 0x06001662 RID: 5730 RVA: 0x000B37A0 File Offset: 0x000B19A0
		private static string Encrypt(Encoding encode, string source)
		{
			byte[] bytes = encode.GetBytes(source);
			string text;
			try
			{
				text = Convert.ToBase64String(bytes);
			}
			catch
			{
				text = source;
			}
			return text;
		}

		// Token: 0x06001663 RID: 5731 RVA: 0x000B37E0 File Offset: 0x000B19E0
		private static string Decode(Encoding encode, string result)
		{
			string text;
			try
			{
				byte[] array = Convert.FromBase64String(result);
				text = encode.GetString(array);
			}
			catch
			{
				text = string.Empty;
			}
			return text;
		}

		// Token: 0x06001664 RID: 5732 RVA: 0x000B3824 File Offset: 0x000B1A24
		private static string getResult(string zz)
		{
			char[] array = new char[]
			{
				'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P',
				'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z',
				'X', 'C', 'V', 'B', 'N', 'M'
			};
			char[] array2 = new char[]
			{
				'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p',
				'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
				'x', 'c', 'v', 'b', 'n', 'm'
			};
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < zz.Length; i++)
			{
				bool flag = Array.IndexOf<char>(array, zz[i]) == -1;
				if (flag)
				{
					bool flag2 = Array.IndexOf<char>(array2, zz[i]) == -1;
					if (flag2)
					{
						stringBuilder.Append(zz[i]);
					}
					else
					{
						for (int j = 0; j < array2.Length; j++)
						{
							bool flag3 = array2[j] == zz[i];
							if (flag3)
							{
								stringBuilder.Append(array[j]);
								break;
							}
						}
					}
				}
				else
				{
					for (int k = 0; k < array.Length; k++)
					{
						bool flag4 = array[k] == zz[i];
						if (flag4)
						{
							stringBuilder.Append(array2[k]);
							break;
						}
					}
				}
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06001665 RID: 5733 RVA: 0x000B3940 File Offset: 0x000B1B40
		public static string Encrypt_Save(string source)
		{
			string text = SaveManager.Encrypt(Encoding.UTF8, source);
			bool flag = text.Substring(text.Length - 1, 1) == "=";
			if (flag)
			{
				text = text.Substring(0, text.Length - 1) + "@";
			}
			text = "@" + text;
			int randomInt = Tools.GetRandomInt(3, 9);
			for (int i = 0; i < randomInt; i++)
			{
				int randomInt2 = Tools.GetRandomInt(1, text.Length - 2);
				text = text.Insert(randomInt2, "#");
			}
			text = text.Replace("/", "$");
			return SaveManager.getResult(text);
		}

		// Token: 0x06001666 RID: 5734 RVA: 0x000B39F8 File Offset: 0x000B1BF8
		public static string Decode_Save(string result)
		{
			bool flag = result.Substring(result.Length - 1, 1) == "@";
			if (flag)
			{
				result = result.Substring(0, result.Length - 1) + "=";
			}
			result = result.Substring(1, result.Length - 1);
			result = result.Replace("#", "").Replace("$", "/");
			result = SaveManager.getResult(result);
			return SaveManager.Decode(Encoding.UTF8, result);
		}

		// Token: 0x06001667 RID: 5735 RVA: 0x000B3A8C File Offset: 0x000B1C8C
		public static string ExtractString(string str)
		{
			str = SaveManager.Decode(Encoding.UTF8, str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2)
				.Replace("/", "")
				.Replace("#", "/"));
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < str.Length; i += 2)
			{
				int num = (int)(str[i + 1] - '0');
				for (int j = 0; j < num; j++)
				{
					stringBuilder.Append(str[i]);
				}
			}
			return stringBuilder.ToString();
		}

		// Token: 0x06001668 RID: 5736 RVA: 0x000B3B40 File Offset: 0x000B1D40
		public static string GetSaveForList(string saveName)
		{
			string text = SaveManager.SaveDir + saveName;
			bool flag = !File.Exists(text);
			string text2;
			if (flag)
			{
				text2 = string.Empty;
			}
			else
			{
				using (StreamReader streamReader = new StreamReader(text))
				{
					string text3 = streamReader.ReadToEnd();
					bool flag2 = SavePanelUI.EN_SAVE() != 0;
					if (flag2)
					{
						text2 = SaveManager.GetDecode(text3, true);
					}
					else
					{
						bool flag3 = text3.StartsWith("@");
						if (flag3)
						{
							text2 = string.Empty;
						}
						else
						{
							text2 = text3;
						}
					}
				}
			}
			return text2;
		}

		// Token: 0x06001669 RID: 5737 RVA: 0x000B3BD8 File Offset: 0x000B1DD8
		private static string SetEncrypt(string content)
		{
			int num = content.IndexOf('>');
			string text = content.Substring(0, num + 1);
			int num2 = content.LastIndexOf('<');
			string text2 = content.Substring(num2, content.Length - num2);
			content = content.Substring(num + 1, num2 - num - 1);
			content = SaveManager.Encrypt_Save(content);
			return text + content + text2;
		}

		// Token: 0x0600166A RID: 5738 RVA: 0x000B3C3C File Offset: 0x000B1E3C
		private static string GetDecode(string content, bool forList = false)
		{
			string text = string.Empty;
			int num = content.IndexOf('>');
			bool flag = num != -1;
			if (flag)
			{
				text = content.Substring(0, num + 1);
			}
			string text2 = string.Empty;
			bool flag2 = forList && text.IndexOf("information=") > 22;
			if (flag2)
			{
				content = string.Empty;
			}
			else
			{
				int num2 = content.LastIndexOf('<');
				bool flag3 = num2 != -1;
				if (flag3)
				{
					text2 = content.Substring(num2, content.Length - num2);
				}
				bool flag4 = num != -1 && num2 != -1;
				if (flag4)
				{
					content = content.Substring(num + 1, num2 - num - 1);
				}
				content = SaveManager.Decode_Save(content);
			}
			return text + content + text2;
		}
	}
}
