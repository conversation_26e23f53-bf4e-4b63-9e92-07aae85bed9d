--[[金庸群侠传X
游戏引擎逻辑扩展脚本]]--
local logger =luanet.import_type('JyGame.FileLogger').instance
local Tools = luanet.import_type('JyGame.Tools')
local Debug = luanet.import_type('UnityEngine.Debug')
local LuaTool = luanet.import_type('JyGame.LuaTool')
local CommonSettings = luanet.import_type('JyGame.CommonSettings')
local RuntimeData = luanet.import_type('JyGame.RuntimeData')
local ModData = luanet.import_type('JyGame.ModData')
local AudioManager = luanet.import_type('JyGame.AudioManager')
local LuaManager = luanet.import_type('JyGame.LuaManager')
local ResourceManager=luanet.import_type('JyGame.ResourceManager')
local Resource=luanet.import_type('JyGame.Resource')
local Role= luanet.import_type('JyGame.Role')
local ItemMenu=luanet.import_type('JyGame.ItemMenu')
local ItemInstance=luanet.import_type('JyGame.ItemInstance')
local Item=luanet.import_type('JyGame.Item')
local SkillStatus=luanet.import_type('JyGame.SkillStatus')
local ITTrigger=luanet.import_type('JyGame.ITTrigger')

local function split(str, reps)  
local resultStrsList = {};  
    string.gsub(str, '[^' .. reps ..']+', function(w) table.insert(resultStrsList, w) end );  
   return resultStrsList;  
end  

local function splitone(strings,splitstrings)
    local a,b=string.find(strings,splitstrings)
    a=string.sub(strings,1,a-1)
    b=string.sub(strings,b+1,-1)
    return a,b
end

local  function GetItemTable()
local t={}
local c=LuaTool.CreateLuaTable(RuntimeData.Instance.Items)
for k,v in pairs (c) do
t[k]={item=v.Key,number=v.Value}
end
return t
end

local function FindItem(name,item)
item=item or GetItemTable()
for k,v in pairs (item) do
if v.item.Name==name then
return v
end
end
return {item=Item.GetItem(name),number=0} 
end

--控制台指令+游戏内的跳转指令
function GameEngine_extendConsole(gameEngine,statusType,value)
	
	if(statusType == "test") then
		print(value)
		--扩展的指令，必须返回true
		return true
	elseif(statusType == "get_money") then
		RuntimeData.Instance.Money = RuntimeData.Instance.Money + tonumber(value)
		AudioManager.Instance:PlayEffect("音效.升级")
		--部分参数生效时，需要刷新一次状态栏
		RuntimeData.Instance.MapUI:RefreshRoleState()
		return true 
	elseif(statusType == "play_music") then
		AudioManager.Instance:Play(value)
		return true
	end
	
	return false
end


-- GameEngine.lua中有关实现
-- 这是控制台或者result跳转的地方；如果想改成action实现也是可以的，具体怎么做可以自行研究
function GameEngine_extendConsole(gameEngine, statusType, value)
    -- 其他原本有的内容保留，这里跳过
    -- 无双塔
    if statusType == "battle_continue" then
        local callback = LuaTool.MakeVoidCallBack(function()
            gameEngine:SwitchGameScene("map", RuntimeData.Instance.CurrentBigMap)
        end)
        local BattleType = luanet.import_type("JyGame.BattleType")
        local Application = luanet.import_type("UnityEngine.Application")
        local params = split(value, "#")
        gameEngine.battleType = BattleType.Common
        if params[2] == "1" then
            gameEngine.BattleSelectRole_CurrentForbbidenKeys:Clear()
        end
        local thisForbidden = {}
        if params[5] then
            local vals = split(params[5], ",")
            for _, v in ipairs(vals) do
                if not gameEngine.BattleSelectRole_CurrentForbbidenKeys:Contains(v) then
                    table.insert(thisForbidden, v)
                    gameEngine.BattleSelectRole_CurrentForbbidenKeys:Add(v)
                end
            end
        end
        gameEngine.CurrentSceneType = "battle"
        gameEngine.CurrentSceneValue = params[1]
        gameEngine.BattleSelectRole_BattleCallback = LuaTool.MakeStringCallBack(function(winOrLose)
            if #thisForbidden > 0 then
                for _, v in ipairs(thisForbidden) do
                    gameEngine.BattleSelectRole_CurrentForbbidenKeys:Remove(v)
                end
            end
            if winOrLose == "win" then
                gameEngine:SwitchGameScene("story", params[3])
            else
                gameEngine:SwitchGameScene("story", params[4])
            end
        end)
        Application.LoadLevel("BattleSelectRole")
        return true
    end
end


--扩展STORY ACTION
function GameEngine_extendStoryAction(this,action,paras,callback)
function selecteq(luafunction,str)
   str=str or "请选择装备"
   local it =RuntimeData.Instance.MapUI.ItemMenu
  local tc=0
   it:Show(str, RuntimeData.Instance.Items,
   LuaTool.MakeObjectCallBack( function(obj)
   it:Hide()
    eq=obj
    --RuntimeData.Instance.MapUI.DialogPanel:GetComponent("DialogUI"):Show("主角",obj.Name, callback)
tc=eq.AdditionTriggers.Count
if (eq.type==1 or eq.type==2 or eq.type==3) and tc<6 then
    luafunction(eq)
else
this:ShowDialog("主角","你选择的【"..eq.Name.."】不是装备或者是已经强化到极限的装备",callback) 
end 
    end),
   LuaTool.MakeVoidCallBack( function(num)  
       this:ShowDialog("主角","你放弃了选择",callback)  
  --  RuntimeData.Instance.gameEngine:SwitchGameScene("map",RuntimeData.Instance.CurrentBigMap) 
    
    end),nil
    )
end


--这是附魔函数
--可选道具【补天石】，这里用蛇胆代替了
function yfumo(eq,pro)
local m=FindItem("补天石")
 local num0=1
local name=eq.Name
local count=eq.AdditionTriggers.Count
local t={}
 if num0 > m.number then
 num0=0
 end
 if Tools.ProbabilityTest(pro*num0)  then
 for i=0,eq.AdditionTriggers.Count-1 do
 local value={eq.AdditionTriggers[i].Name,eq.AdditionTriggers[i].ArgvsString}
    table.insert(t,value)
    end      
    local eq2=ItemInstance.Generate(name,false)
    eq2:AddRandomTriggers(count+1) 
     if (eq2.type ==1 or eq2.type ==2 or eq2.type ==3) and (count<6)   then
RuntimeData.Instance:addItem(eq,-1)
    for i=0,#t-1 do
    eq2.AdditionTriggers[i].Name=t[i+1][1]
    eq2.AdditionTriggers[i].ArgvsString=t[i+1][2]
    end
RuntimeData.Instance:addItem(eq2,1)
this:ShowDialog("主角","你的"..eq.Name.."增益附魔成功", callback) 
else
this:ShowDialog("主角","你选择的"..eq.Name.."不是装备,或者属性条数已经达到上限，不能附魔", callback) 
     end          
  else
 RuntimeData.Instance:addItem(eq,-1)
if Tools.ProbabilityTest(eq.level/10) then
RuntimeData.Instance:addItem( Item.GetItem("晶银矿石"),Tools.GetRandomInt(1,count+1)) 
this:ShowDialog("主角","运气不错，【"..eq.Name.."】换到了【晶银矿石】！", callback)
else  
this:ShowDialog("主角","没有幸运女神眷顾的我，就这么失去了【"..eq.Name.."】。真是遗憾呀！", callback)
end  
   end
end




--这是一键附魔函数
--这里设计是消耗更高级道具【精髓附魔石】
--为了方便测试还是用蛇胆
function xfumo(eq)
local km=FindItem("铜矿石")
local name=eq.Name
if km.number>5 then
 local eq2=ItemInstance.Generate(name,false)
    eq2:AddRandomTriggers(6)
 RuntimeData.Instance:addItem(eq,-1)
RuntimeData.Instance:addItem(Item.GetItem("铜矿石"),-6)
RuntimeData.Instance:addItem(eq2,1)
this:ShowDialog("主角","装备【"..eq.Name.."】一键附魔成功",callback)  
else
this:ShowDialog("主角","材料不足，无法执行一键附魔",callback)  
end
end

--铁矿石函数(测试时为蛇胆)
function yuntie(eq)
action.value="主角#是否添加【铁矿石】(每个提升5%成功率，最多十个)#是#否"
this:LoadSelection(action,LuaTool.MakeIntCallBack( function(num)
    if num==0 then
this.nameInputPanel:Show("输入你要用的【铁矿石】数目",LuaTool.MakeStringCallBack( function(num1)
local n=FindItem("铁矿石")
local num2=0
if tonumber(num1) then
    num2=tonumber(num1)
      end
      if num2>10 then
        num2=10
      end
      if num2>n.number then
        num2=n.number
      end        RuntimeData.Instance:addItem(Item.GetItem("铁矿石"),-1*num2)
        yfumo(eq,0.4+0.05*num2)
        end))
    else
    yfumo(eq,0.4)
    end
   end))
end

function exfumo()
action.value="主角#确认附魔？".."#".."是的我要增益附魔#我要一键附魔".." #我要放弃本次附魔"
this:LoadSelection(action,LuaTool.MakeIntCallBack( function(num0)
if num0==0 then
selecteq(yuntie,"请选择你要附魔的装备")
elseif num0==1 then
selecteq(xfumo,"请选择你要一键附魔的装备")
else
this:ShowDialog("主角","你放弃了选择",callback)  
end
end))
end

if (action.type=="UPITEM") then
 exfumo()
 return true
end


	if(action.type == "TEST_PRINT") then
		print(action.value)
		--继续执行下一条ACTION
		this:ExecuteNextStoryAction(callback)
		return true
	end
if(action.type == "RANDOMITEM") then
		local a = string.split(action.value,"#")
		for i=1,a[2] do
			RuntimeData.Instance:addItem(ItemInstance.Generate(a[1], true), 1)
		end
		this:ExecuteNextStoryAction(callback)
		return true
	end	
	return false
end

--扩展江湖历练UI面板
function GameEngine_jianghuContent(this)

	local result
                                                                                  	        
	result = "<color='#007d65'>" .. RuntimeData.Instance.maleName .. "</color>生活技能属性：\n"	
        result = result .. "  才识：<color='#543044'>" .. RuntimeData.Instance:getHaogan("才识") .. "</color>\n"	 
        result = result .. "  琴艺：<color='#543044'>" .. RuntimeData.Instance:getHaogan("琴艺") .. "</color>\n"
        result = result .. "  女红：<color='#543044'>" .. RuntimeData.Instance:getHaogan("裁缝") .. "</color>\n"        
        result = result .. "  医术：<color='#543044'>" .. RuntimeData.Instance:getHaogan("医术") .. "</color>\n" 
        result = result .. "  毒术：<color='#543044'>" .. RuntimeData.Instance:getHaogan("毒术") .. "</color>\n"	                  
	result = result .. "\n"          	
	result = result .. "\n"	
	result = result .. "男性角色关系：\n"	
        result = result .. "    沈   飞（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("沈飞") .. "</color>）\n"
        result = result .. "    段   誉（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("段誉") .. "</color>）\n"        
        result = result .. "    郭   靖（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("郭靖") .. "</color>）\n"
        result = result .. "    狄   云（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("狄云") .. "</color>）\n"
	result = result .. "    颜重光（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("颜重光") .. "</color>）\n"
        result = result .. "    宋青书（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("宋青书") .. "</color>）\n"
        result = result .. "    欧阳克（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("欧阳克") .. "</color>）\n"
        result = result .. "    李文秀（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("李文秀") .. "</color>）\n"
        result = result .. "    林平之（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("林平之") .. "</color>）\n" 
        result = result .. "    袁冠南（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("袁冠南") .. "</color>）\n"        
        result = result .. "    杨   过    古墓派（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("杨过") .. "</color>）\n"        
        result = result .. "    令狐冲    日月教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("令狐冲") .. "</color>）\n" 
        result = result .. "    袁承志    五毒教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("袁承志") .. "</color>）\n" 
        result = result .. "    齐云璈    五毒教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("齐云璈") .. "</color>）\n"                
        result = result .. "    东方不败 日月神教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("东方不败") .. "</color>）\n"
	result = result .. "\n"                 
	result = result .. "女性角色关系：\n"

        result = result .. "    石   淇（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("石淇") .. "</color>）\n"    
        result = result .. "    飞   飞（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("白飞飞") .. "</color>）\n"
        result = result .. "    上官仙（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("上官仙") .. "</color>）\n"
        result = result .. "    王语嫣（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("王语嫣") .. "</color>）\n"
        result = result .. "    李青萝（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("李青萝") .. "</color>）\n" 
	result = result .. "    姬   雪（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("姬雪") .. "</color>）\n"  
        result = result .. "    夷   光（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("夷光") .. "</color>）\n" 
        result = result .. "    黄   蓉（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("黄蓉") .. "</color>）\n"
        result = result .. "    水   笙（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("水笙") .. "</color>）\n"
        result = result .. "    香   香（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("香香") .. "</color>）\n"          
        result = result .. "    骆   冰（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("骆冰") .. "</color>）\n"
	result = result .. "    阿   曼（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("阿曼") .. "</color>）\n"
        result = result .. "    阿   紫（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("阿紫") .. "</color>）\n"
        result = result .. "    李   冶（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("李冶") .. "</color>）\n"    
        result = result .. "    木婉清（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("木婉清") .. "</color>）\n"
	result = result .. "    计老人（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("上官虹") .. "</color>）\n"
        result = result .. "    岳灵珊（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("岳灵珊") .. "</color>）\n" 
        result = result .. "    林瓶儿（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("林瓶儿") .. "</color>）\n"   
        result = result .. "    霍青桐（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("霍青桐") .. "</color>）\n" 
        result = result .. "    凌霜华（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("凌霜华") .. "</color>）\n"                          
        result = result .. "    萧中慧（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("萧中慧") .. "</color>）\n"
        result = result .. "    灵鹫使婢 灵鹫宫（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("菊剑") .. "</color>）\n"        
        result = result .. "    符敏仪    灵鹫宫（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("符敏仪") .. "</color>）\n" 
        result = result .. "    李秋水    灵鹫宫（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("李秋水") .. "</color>）\n"        
        result = result .. "    小龙女    古墓派（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("小龙女") .. "</color>）\n"
        result = result .. "    任盈盈    日月教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("任盈盈") .. "</color>）\n"
        result = result .. "    曲非烟    日月教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("曲非烟") .. "</color>）\n"
        result = result .. "    夏青青    五毒教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("温青青") .. "</color>）\n"
        result = result .. "    蓝凤凰    五毒教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("蓝凤凰") .. "</color>）\n"
        result = result .. "    唐   蓝    五毒教（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("唐蓝") .. "</color>）\n"   
        result = result .. "    戚   芳    无门派（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("戚芳") .. "</color>）\n"           
                                                                                                 		                             
	result = result .. "\n"  	                        
	result = result .. "\n"  
	result = result .. "赞助版角色关系：\n"
        result = result .. "    张无忌（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("张无忌") .. "</color>）\n"         
        result = result .. "    赵   敏（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("赵敏") .. "</color>）\n"
        result = result .. "    小   昭（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("小昭") .. "</color>）\n" 
        result = result .. "    周芷若（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("周芷若") .. "</color>）\n" 
        result = result .. "    苗若兰（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("苗若兰") .. "</color>）\n" 
        result = result .. "    程灵素（<color='#6950a1'>" .. RuntimeData.Instance:getHaogan("程灵素") .. "</color>）\n" 
                                                                                                            		                             
	result = result .. "\n"  
	result = result .. "\n" 
	result = result .. "\n" 	 
	result = result .. "其它信息：\n"	
	result = result .. "  本周目生命、内力上限：<color='#69541b'>" .. CommonSettings.MAX_HPMP .. "</color>\n"
	result = result .. "  松鼠旅馆箱子存储量：<color='#69541b'>" .. RuntimeData.Instance.XiangziCount .. "/" .. RuntimeData.Instance.MaxXiangziItemCount .. "</color>\n"
	result = result .. "  总共击杀<color='#69541b'>" .. ModData.GetParam(ModData.TOTALKILL_KEY) .. "</color>个敌人\n"
	result = result .. "  总共通关了<color='#69541b'>" .. ModData.GetParam(ModData.END_COUNT_KEY) .. "</color>次\n"
	result = result .. "  达过的最高周目：<color='#69541b'>" .. math.max(unpack({RuntimeData.Instance.Round, ModData.GetParam(ModData.MAX_ROUND_KEY)})) .. "</color>周目\n"
	result = result .. "  最终存档位置：<color='#69541b'>" .. RuntimeData.Instance.maleName .. "</color>存在了<color='#69541b'>" .. ModData.GetParam(ModData.last_save_index) .. "</color>号档\n"
	result = result .. "\n" 	
	result = result .. "\n"  
	result = result .. "\n"                                                                                                                                                         	   

	return result

end
	
function string.split(str, delimiter)
	if str==nil or str=='' or delimiter==nil then
		return nil
	end
	
    local result = {}
    for match in (str..delimiter):gmatch("(.-)"..delimiter) do
        table.insert(result, match)
    end
    return result
end