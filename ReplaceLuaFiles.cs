using System;
using System.IO;

namespace ReplaceLuaFiles
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("替换Lua文件工具");
            Console.WriteLine("================");
            Console.WriteLine("将解密后的lua文件替换原来的加密文件");
            Console.WriteLine();
            
            string luaDir = @"gamedata\suyu\HYYM\lua";
            
            if (!Directory.Exists(luaDir))
            {
                Console.WriteLine($"找不到lua目录: {luaDir}");
                return;
            }
            
            // 获取所有解密后的lua文件
            string[] decryptedFiles = Directory.GetFiles(luaDir, "*_decrypted.lua");
            
            int successCount = 0;
            int failCount = 0;
            
            foreach (string decryptedFile in decryptedFiles)
            {
                try
                {
                    // 获取原始文件名（去掉_decrypted后缀）
                    string fileName = Path.GetFileName(decryptedFile);
                    string originalFileName = fileName.Replace("_decrypted.lua", ".lua");
                    string originalFile = Path.Combine(luaDir, originalFileName);
                    
                    Console.WriteLine($"处理文件: {originalFileName}");
                    
                    // 备份原文件
                    if (File.Exists(originalFile))
                    {
                        string backupFile = originalFile + ".bak";
                        File.Copy(originalFile, backupFile, true);
                        Console.WriteLine($"  已备份原文件为: {Path.GetFileName(backupFile)}");
                    }
                    
                    // 复制解密后的文件到原文件位置
                    File.Copy(decryptedFile, originalFile, true);
                    Console.WriteLine($"  替换成功");
                    
                    successCount++;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  处理失败: {ex.Message}");
                    failCount++;
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("处理完成！");
            Console.WriteLine($"成功替换: {successCount} 个文件");
            Console.WriteLine($"失败文件: {failCount} 个文件");
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
