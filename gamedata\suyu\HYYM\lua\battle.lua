﻿--[[
金庸群侠传X 战斗逻辑扩展

]]--

local Tools = luanet.import_type('JyGame.Tools')
local Debug = luanet.import_type('UnityEngine.Debug')
local LuaTool = luanet.import_type('JyGame.LuaTool')
local CommonSettings = luanet.import_type('JyGame.CommonSettings')
local RuntimeData = luanet.import_type('JyGame.RuntimeData')
local Color = luanet.import_type('UnityEngine.Color')

local Item = luanet.import_type('JyGame.Item')
local ItemInstance = luanet.import_type('JyGame.ItemInstance')
--local RuntimeData = luanet.import_type('JyGame.RuntimeData')



--在某个角色行动之前调用
function BATTLE_BeforeRoleAction(battlefield, currentSprite)
	--currentSprite:AttackInfo("轮到我行动啦!", Color.black)

	if currentSprite.Role:HasTalent("搔首弄姿") and Tools.ProbabilityTest(0.2) then
  		if currentSprite:AddBuffOnly2("定身",0,1)==true then
    			currentSprite:AttackInfo("哎呀……别盯着看我的臀部……",Color.magenta)
  		end
	end
	if currentSprite:HasBuff("魅影绝世") then
		local x1 = math.random(-6, 6)
		local y1 = math.random(-2, 2)
		local x = math.clamp(currentSprite.X + x1, 0, 10)
		local y = math.clamp(currentSprite.Y + y1, 0, 3)

		if not battlefield:GetSprite(x, y) then
			currentSprite:SetPos(x, y)
			currentSprite:AttackInfo("无影，无踪！", Color.black)
			currentSprite:DeleteBuff("魅影绝世")
		end
	end
end


--角色施展技能之后(播放完技能攻击动画）调用
function BATTLE_AfterSkillAnimation(battlefield, currentSprite, skill, hitnumber)
	-- 天赋：百战，命中 4 人或以上恢复最大生命的 10%
	if currentSprite.Role:HasTalent("百战") and hitnumber >= 4 then
		local hpAdd = math.min(math.ceil(currentSprite.MaxHp * 0.1), currentSprite.MaxHp - currentSprite.Hp)
		if hpAdd > 0 then
			currentSprite.Hp = currentSprite.Hp + hpAdd
			currentSprite:AttackInfo("+" .. hpAdd, Color.green)
			currentSprite:RandomSay(LuaTool.MakeStringArray({"全都上吧!", "哼！"}))
		end
	end
end

--角色技能命中目标后（播放技能攻击动画之前）调用
--hitnumber : 命中目标个数
function BATTLE_BeforeSkillAnimation(battlefield, currentSprite, skill, hitnumber)
	--currentSprite:AttackInfo("我打中了"..hitnumber.."个人！", Color.white)
	
	--例子天赋：空挥狂魔，没有打中人的情况下，自动攻击最近的一个人敌人
	if(currentSprite.Role:HasTalent("空挥狂魔") and hitnumber == 0) then
		--寻找最近的敌人
		mindist = 9999
		findSprite = nil
		for _,sprite in pairs(battlefield.SpritesTable) do
			if sprite.Team ~= currentSprite.Team then
				local distx = math.abs(currentSprite.X - sprite.X)
				local disty = math.abs(currentSprite.Y - sprite.Y)
				local dist = distx + disty
				if(dist < mindist) then
					mindist = dist
					findSprite = sprite
				end
			end
		end
		--如果找到了，攻击他
		if(findSprite ~= nil) then
			battlefield:Attack(currentSprite, skill, findSprite.X, findSprite.Y)
		end
	end
end

--角色濒死时调用
function BATTLE_Die(battlefield, sprite, attackResult)
	local isDc=false
	local findSprite1 = nil
	for _,sprite1 in pairs(battlefield.SpritesTable) do
		if  (sprite1.Role:HasTalent("神之妙手") and sprite1.Team == sprite.Team and sprite1~=sprite) then
			findSprite1=sprite1
			isDc=true
			break
		end
	end

	if isDc==true then
		if(Tools.ProbabilityTest(0.99)) then 
			battlefield:Log("神医【"..findSprite1.Role.Name.."】对【"..sprite.Role.Name.."】发动了【神之妙手】")
			sprite:Say("谢谢神医相助")
			sprite.Hp = sprite.MaxHp*0.8
			sprite.Mp = sprite.MaxMp*0.5
			sprite:Refresh()
			return false
		end
	end
	--天赋“至死不渝”， 80%概率不死，剩1滴血
	if sprite.Role:HasTalent("至死不渝") and Tools.ProbabilityTest(0.8) then
        	battlefield:Log("【" .. sprite.Role.Name .. "】触发天赋【至死不渝】，奇迹生还！")
        	sprite:Say("就是死不了，你打我啊！")
        	sprite.Hp = 1
        	return false -- 阻止角色死亡
    	end

	return true
end

--角色休息时调用
function BATTLE_Rest(battlefield, sprite, hprecover, mprecover)
	--sprite:Say("我在休息...")
		
	if(sprite.Role:HasTalent("淫猥下流")) then		
        	hprecover = hprecover+math.floor((sprite.MaxHp-sprite.Hp)*0.1)
        	sprite:Say("还想要……还想要更多……")
        	if(Tools.ProbabilityTest(0.2)) then
			sprite:AddBuff("晕眩",1,1)
--			sprite:Say("啊……好舒服……")
		end
	end
	
	--固定返回格式
	return string.format("%s,%s", hprecover, mprecover)
end
