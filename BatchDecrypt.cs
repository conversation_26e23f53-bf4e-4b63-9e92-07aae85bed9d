using System;
using System.IO;
using System.Security.Cryptography;
using System.Text;

namespace BatchDecrypt
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("批量解密XML文件工具");
            Console.WriteLine("====================");
            
            string scriptsPath = @"gamedata\suyu\HYYM\Scripts";
            
            if (!Directory.Exists(scriptsPath))
            {
                Console.WriteLine($"找不到Scripts文件夹: {scriptsPath}");
                return;
            }
            
            string[] xmlFiles = Directory.GetFiles(scriptsPath, "*.xml", SearchOption.TopDirectoryOnly);
            Console.WriteLine($"找到 {xmlFiles.Length} 个XML文件");
            
            int successCount = 0;
            int skipCount = 0;
            int failCount = 0;
            
            foreach (string file in xmlFiles)
            {
                string fileName = Path.GetFileName(file);
                
                // 跳过已经解密的文件
                if (fileName.StartsWith("decrypt_"))
                {
                    Console.WriteLine($"跳过已解密文件: {fileName}");
                    skipCount++;
                    continue;
                }
                
                try
                {
                    Console.WriteLine($"处理文件: {fileName}");
                    
                    string content = File.ReadAllText(file, Encoding.UTF8);
                    
                    // 检查是否已经是明文
                    if (content.StartsWith("<"))
                    {
                        Console.WriteLine($"  文件已是明文格式，跳过");
                        skipCount++;
                        continue;
                    }
                    
                    string decryptedContent = DecryptContent(content);
                    
                    if (!string.IsNullOrEmpty(decryptedContent))
                    {
                        // 生成解密后的文件名
                        string directory = Path.GetDirectoryName(file);
                        string fileNameWithoutExt = Path.GetFileNameWithoutExtension(file);
                        string extension = Path.GetExtension(file);
                        string outputPath = Path.Combine(directory, $"decrypt_{fileNameWithoutExt}{extension}");
                        
                        File.WriteAllText(outputPath, decryptedContent, Encoding.UTF8);
                        Console.WriteLine($"  解密成功，保存为: decrypt_{fileNameWithoutExt}{extension}");
                        successCount++;
                    }
                    else
                    {
                        Console.WriteLine($"  解密失败");
                        failCount++;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  处理文件时出错: {ex.Message}");
                    failCount++;
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("处理完成！");
            Console.WriteLine($"成功解密: {successCount} 个文件");
            Console.WriteLine($"跳过文件: {skipCount} 个文件");
            Console.WriteLine($"失败文件: {failCount} 个文件");
            
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        
        static string DecryptContent(string content)
        {
            try
            {
                // 检查是否是CRC格式 (数字@内容)
                if (content.Contains("@"))
                {
                    return DecryptCrcFormat(content);
                }
                // 检查是否是ExtractString格式 (以@开头)
                else if (content.StartsWith("@"))
                {
                    return ExtractString(content);
                }
                else
                {
                    // 尝试其他解密方法
                    return DecodeSave(content);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  解密过程中出错: {ex.Message}");
                return null;
            }
        }

        // CRC格式解密 (对应SaveManager中的crcm方法)
        static string DecryptCrcFormat(string input)
        {
            string[] parts = input.Split(new char[] { '@' });
            if (parts.Length != 2)
            {
                return string.Empty;
            }

            string crcCode = parts[0];
            string encryptedContent = TripleDESDecrypt(parts[1]);
            if (string.IsNullOrEmpty(encryptedContent))
            {
                return string.Empty;
            }
            
            string calculatedCrc = CalculateCRC16(encryptedContent);

            if (crcCode != calculatedCrc)
            {
                Console.WriteLine($"    CRC校验失败: 期望{crcCode}, 实际{calculatedCrc}");
                return string.Empty;
            }

            return encryptedContent;
        }

        // TripleDES解密 (对应SaveManager中的m方法)
        static string TripleDESDecrypt(string encryptedText)
        {
            try
            {
                UTF8Encoding utf8 = new UTF8Encoding();
                using (MD5 md5 = MD5.Create())
                {
                    byte[] key = md5.ComputeHash(utf8.GetBytes("Yh$45Ct@mods"));
                    
                    using (TripleDES tripleDES = TripleDES.Create())
                    {
                        tripleDES.Key = key;
                        tripleDES.Mode = CipherMode.ECB;
                        tripleDES.Padding = PaddingMode.PKCS7;

                        byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
                        byte[] decryptedBytes = tripleDES.CreateDecryptor().TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);
                        
                        return utf8.GetString(decryptedBytes);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    TripleDES解密错误: {ex.Message}");
                return string.Empty;
            }
        }

        // CRC16计算 (对应SaveManager中的CRC16_C方法)
        static string CalculateCRC16(string str)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(str);
            byte crcHigh = 255;
            byte crcLow = 255;
            byte poly1 = 1;
            byte poly2 = 160;

            foreach (byte b in bytes)
            {
                crcHigh ^= b;
                for (int i = 0; i <= 7; i++)
                {
                    byte tempHigh = crcLow;
                    byte tempLow = crcHigh;
                    crcLow = (byte)(crcLow >> 1);
                    crcHigh = (byte)(crcHigh >> 1);
                    
                    if ((tempHigh & 1) == 1)
                    {
                        crcHigh |= 128;
                    }
                    
                    if ((tempLow & 1) == 1)
                    {
                        crcLow ^= poly2;
                        crcHigh ^= poly1;
                    }
                }
            }

            return string.Format("{0}{1}", crcLow, crcHigh);
        }

        // ExtractString解密 (对应SaveManager中的ExtractString方法)
        static string ExtractString(string str)
        {
            str = Base64Decode(str.Replace('\\', '0').Replace('_', '1').Substring(1, str.Length - 2)
                .Replace("/", "")
                .Replace("#", "/"));
            
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < str.Length; i += 2)
            {
                int count = (int)(str[i + 1] - '0');
                for (int j = 0; j < count; j++)
                {
                    result.Append(str[i]);
                }
            }
            return result.ToString();
        }

        // Base64解码
        static string Base64Decode(string encodedText)
        {
            try
            {
                byte[] bytes = Convert.FromBase64String(encodedText);
                return Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return string.Empty;
            }
        }

        // 保存文件解密 (对应SaveManager中的Decode_Save方法)
        static string DecodeSave(string result)
        {
            if (result.Substring(result.Length - 1, 1) == "@")
            {
                result = result.Substring(0, result.Length - 1) + "=";
            }
            result = result.Substring(1, result.Length - 1);
            result = result.Replace("#", "").Replace("$", "/");
            result = GetResult(result);
            return Base64Decode(result);
        }

        // 字符转换 (对应SaveManager中的getResult方法)
        static string GetResult(string input)
        {
            char[] upperChars = new char[]
            {
                'Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P',
                'A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'Z',
                'X', 'C', 'V', 'B', 'N', 'M'
            };
            char[] lowerChars = new char[]
            {
                'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p',
                'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', 'z',
                'x', 'c', 'v', 'b', 'n', 'm'
            };

            StringBuilder result = new StringBuilder();
            for (int i = 0; i < input.Length; i++)
            {
                int upperIndex = Array.IndexOf(upperChars, input[i]);
                if (upperIndex == -1)
                {
                    int lowerIndex = Array.IndexOf(lowerChars, input[i]);
                    if (lowerIndex == -1)
                    {
                        result.Append(input[i]);
                    }
                    else
                    {
                        result.Append(upperChars[lowerIndex]);
                    }
                }
                else
                {
                    result.Append(lowerChars[upperIndex]);
                }
            }
            return result.ToString();
        }
    }
}
