using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Text;
using LuaInterface;
using UnityEngine;

namespace JyGame
{
	// Token: 0x02000222 RID: 546
	public static class LuaManager
	{
		// Token: 0x060013F9 RID: 5113 RVA: 0x0009A924 File Offset: 0x00098B24
		public static byte[] JyGameLuaLoader(string path)
		{
			byte[] array;
			if (CommonSettings.MOD_KEY() >= 0)
			{
				if (path.StartsWith("jygame/"))
				{
					string text = ModManager.ModBaseUrlPath + "lua/" + path.Replace("jygame/", string.Empty);
					Debug.Log("loading lua file : " + text);
					using (StreamReader streamReader = new StreamReader(text))
					{
						string text2;
						if (GlobalData.CurrentMod.enc)
						{
							text2 = ((!GlobalData.CurrentMod.oldenc) ? SaveManager.ExtractString(streamReader.ReadToEnd()) : SaveManager.crcm(streamReader.ReadToEnd()));
						}
						else
						{
							text2 = streamReader.ReadToEnd();
							if (text2.Length > 100 && text2.StartsWith("@"))
							{
								text2 = SaveManager.ExtractString(text2);
							}
						}
						return new UTF8Encoding(true).GetBytes(text2);
					}
				}
				string text3 = "TextAssets/lua/" + path;
				Debug.Log("loading lua file : " + text3);
				array = Resource.GetBytes("TextAssets/lua/" + path, false);
			}
			else
			{
				string text4 = "TextAssets/lua/" + path;
				Debug.Log("loading lua file : " + text4);
				array = Resource.GetBytes("TextAssets/lua/" + path, false);
			}
			return array;
		}

		// Token: 0x060013FA RID: 5114 RVA: 0x0000683D File Offset: 0x00004A3D
		public static void Reload()
		{
			TriggerLogic.ClearluaExtensionConditions();
			LuaManager._luaConfig = null;
			LuaManager.Init(true);
		}

		// Token: 0x060013FB RID: 5115 RVA: 0x0009AA78 File Offset: 0x00098C78
		public static void Init(bool forceReset = false)
		{
			if (forceReset)
			{
				LuaManager._inited = false;
				if (LuaManager._lua != null)
				{
					LuaManager._lua.Destroy();
				}
			}
			if (!LuaManager._inited)
			{
				LuaManager._lua = new LuaScriptMgr();
				LuaManager._lua.Start();
				try
				{
					foreach (string text in LuaManager.files)
					{
						LuaManager._lua.DoFile("jygame/" + text);
					}
				}
				catch (Exception ex)
				{
					Debug.LogError(ex.ToString());
					FileLogger.instance.LogError("============LUA语法错误！===========");
					FileLogger.instance.LogError(ex.ToString());
				}
				LuaManager._inited = true;
				LuaTable luaTable = LuaManager.Call<LuaTable>("ROOT_getLuaFiles", new object[0]);
				try
				{
					foreach (object obj in luaTable.Values)
					{
						string text2 = (string)obj;
						LuaManager._lua.DoFile("jygame/" + text2);
					}
				}
				catch (Exception ex2)
				{
					Debug.LogError(ex2.ToString());
					FileLogger.instance.LogError("============LUA语法错误！===========");
					FileLogger.instance.LogError(ex2.ToString());
				}
			}
		}

		// Token: 0x060013FC RID: 5116 RVA: 0x0009ABE8 File Offset: 0x00098DE8
		public static object[] Call(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			object[] array;
			if (luaFunction == null)
			{
				Debug.LogError("调用了未定义的lua 函数:" + functionName);
				array = null;
			}
			else
			{
				array = luaFunction.Call(paras);
			}
			return array;
		}

		// Token: 0x060013FD RID: 5117 RVA: 0x0009AC34 File Offset: 0x00098E34
		public static T Call<T>(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			T t;
			if (luaFunction == null)
			{
				Debug.LogError("调用了未定义的lua 函数:" + functionName);
				t = default(T);
			}
			else
			{
				object[] array = luaFunction.Call(paras);
				if (array.Length == 0 || (array[0] is bool && !(bool)array[0]))
				{
					t = default(T);
				}
				else
				{
					t = (T)((object)array[0]);
				}
			}
			return t;
		}

		// Token: 0x060013FE RID: 5118 RVA: 0x0009ACBC File Offset: 0x00098EBC
		public static int CallWithIntReturn(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			int num;
			if (luaFunction == null)
			{
				Debug.LogError("调用了未定义的lua 函数:" + functionName);
				num = -1;
			}
			else
			{
				num = Convert.ToInt32(luaFunction.Call(paras)[0]);
			}
			return num;
		}

		// Token: 0x060013FF RID: 5119 RVA: 0x0009AD10 File Offset: 0x00098F10
		public static T GetConfig<T>(string key)
		{
			if (LuaManager._luaConfig == null)
			{
				LuaTable luaTable = LuaManager.Call<LuaTable>("ROOT_getConfigList", new object[0]);
				LuaManager._luaConfig = new Dictionary<string, object>();
				foreach (object obj in luaTable)
				{
					DictionaryEntry dictionaryEntry = (DictionaryEntry)obj;
					LuaManager._luaConfig.Add(dictionaryEntry.Key.ToString(), dictionaryEntry.Value);
				}
			}
			return (T)((object)LuaManager._luaConfig[key]);
		}

		// Token: 0x06001400 RID: 5120 RVA: 0x00006850 File Offset: 0x00004A50
		public static string GetConfig(string key)
		{
			return LuaManager.GetConfig<string>(key);
		}

		// Token: 0x06001401 RID: 5121 RVA: 0x00006858 File Offset: 0x00004A58
		public static int GetConfigInt(string key)
		{
			return Convert.ToInt32(LuaManager.GetConfig<object>(key));
		}

		// Token: 0x06001402 RID: 5122 RVA: 0x00006865 File Offset: 0x00004A65
		public static double GetConfigDouble(string key)
		{
			return Convert.ToDouble(LuaManager.GetConfig<object>(key));
		}

		// Token: 0x06001403 RID: 5123 RVA: 0x0009ADB0 File Offset: 0x00098FB0
		public static string CallWithStringReturn(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			string text;
			if (luaFunction == null)
			{
				Debug.LogError("调用了未定义的lua 函数:" + functionName);
				text = string.Empty;
			}
			else
			{
				text = Convert.ToString(luaFunction.Call(paras)[0]);
			}
			return text;
		}

		// Token: 0x06001404 RID: 5124 RVA: 0x00006872 File Offset: 0x00004A72
		public static void null_luaConfig()
		{
			LuaManager._luaConfig = null;
		}

		// Token: 0x06001405 RID: 5125 RVA: 0x0009AE08 File Offset: 0x00099008
		public static float CallWithFloatReturn(string functionName, params object[] paras)
		{
			if (!LuaManager._inited)
			{
				LuaManager.Init(false);
			}
			LuaFunction luaFunction = LuaManager._lua.GetLuaFunction(functionName);
			float num;
			if (luaFunction == null)
			{
				Debug.LogError("调用了未定义的lua 函数:" + functionName);
				num = 0f;
			}
			else
			{
				num = (float)Convert.ToDouble(luaFunction.Call(paras)[0]);
			}
			return num;
		}

		// Token: 0x040007E9 RID: 2025
		private static string[] files = new string[] { "main.lua", "test.lua" };

		// Token: 0x040007EA RID: 2026
		private static bool _inited = false;

		// Token: 0x040007EB RID: 2027
		public static LuaScriptMgr _lua;

		// Token: 0x040007EC RID: 2028
		private static Dictionary<string, object> _luaConfig;
	}
}
